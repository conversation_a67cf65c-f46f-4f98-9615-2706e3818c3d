# 实验三：基于时间序列任务的神经网络建模与测试

## 项目概述

本项目实现了基于RNN、LSTM和Transformer的时间序列预测模型，用于航空乘客数量预测任务。通过对比不同模型的性能，分析各种神经网络架构在时间序列建模中的优势与不足。

## 文件结构

```
实验三/
├── requirements.txt          # 依赖包列表
├── data_preprocessing.py     # 数据预处理模块
├── models.py                # 神经网络模型定义
├── trainer.py               # 模型训练器
├── main_experiment.py       # 主实验文件
├── test_setup.py            # 环境测试文件
├── report_generator.py      # 实验报告生成器
├── visualize_data.py        # 数据可视化脚本
└── README.md               # 项目说明文档
```

## 环境要求

- Python 3.8+
- PyTorch 1.9.0+
- NumPy 1.21.0+
- Pandas 1.3.0+
- Matplotlib 3.4.0+
- Scikit-learn 1.0.0+

## 安装依赖

```bash
pip install -r requirements.txt
```

## 数据集说明

本实验使用**长时间序列数据集**，专门为深度学习模型设计：

- **时间跨度**: 1850年1月开始，默认3000个数据点（约250年的月度数据）
- **数据特征**: 单变量时间序列，包含复杂的时间模式
- **数据复杂性**:
  - 长期指数增长趋势
  - 年度季节性模式（12个月周期）
  - 经济周期（7-10年周期）
  - 随机游走成分
  - 偶发事件影响（战争、经济危机等）
  - 技术革新带来的增长突破
  - 异常值和噪声

**数据优势**:
- **足够长度**: 3000个数据点为深度学习模型提供充足的训练数据
- **复杂模式**: 包含多种时间序列模式，更好地测试模型能力
- **真实性**: 模拟真实世界的复杂时间序列特征

**注意**: 代码自动生成长时间序列数据，无需下载外部数据集。如果您想使用真实数据集，可以考虑：

- [Yahoo Finance 股票数据](https://finance.yahoo.com/) - 长期股价数据
- [FRED Economic Data](https://fred.stlouisfed.org/) - 经济时间序列数据
- [Climate Data](https://www.ncdc.noaa.gov/) - 气候时间序列数据

下载后将CSV文件放在项目根目录，并在`data_preprocessing.py`中指定文件路径。

## 模型架构

### 1. RNN模型
- 基础的循环神经网络
- 2层RNN，隐藏单元数64
- 包含Dropout防止过拟合

### 2. LSTM模型
- 长短期记忆网络
- 解决RNN的梯度消失问题
- 更好地捕获长期依赖关系

### 3. Transformer模型
- 基于自注意力机制
- 并行计算能力强
- 能够捕获长距离依赖关系

## 快速开始

### 1. 环境测试
首先运行环境测试，确保所有依赖正确安装：

```bash
python test_setup.py
```

### 2. 数据可视化（可选）
查看长时间序列数据的特点和复杂性：

```bash
python visualize_data.py
```

### 3. 运行完整实验
运行主实验文件，进行模型训练和对比：

```bash
python main_experiment.py
```

### 4. 生成实验报告
生成详细的实验报告模板：

```bash
python report_generator.py
```

## 实验内容

1. **数据预处理**: 数据加载、标准化、序列化
2. **模型训练**: 训练RNN、LSTM、Transformer三种模型
3. **性能对比**: 比较不同模型的MSE、MAE、MAPE指标
4. **超参数实验**: 测试不同隐藏单元数对性能的影响
5. **结果可视化**: 绘制训练曲线和预测结果

## 评估指标

- **MSE (均方误差)**: 衡量预测值与真实值的平方差
- **MAE (平均绝对误差)**: 衡量预测值与真实值的绝对差
- **MAPE (平均绝对百分比误差)**: 衡量相对预测误差

## 预期结果

根据时间序列预测的一般经验：

1. **LSTM** 通常在时间序列任务中表现最好，能够有效捕获长期依赖
2. **RNN** 可能存在梯度消失问题，在长序列上性能较差
3. **Transformer** 在足够数据的情况下可能表现优异，但在小数据集上可能过拟合

## 注意事项

1. 首次运行时会自动生成模拟的航空乘客数据
2. 训练过程可能需要几分钟到十几分钟，取决于硬件配置
3. 如果有GPU，代码会自动使用GPU加速训练
4. 所有图表会自动显示，请确保图形界面可用

## 故障排除

### 常见问题

1. **导入错误**: 确保所有依赖包已正确安装
2. **CUDA错误**: 如果没有GPU，代码会自动使用CPU
3. **内存不足**: 可以减小批大小或模型大小
4. **中文字体问题**: 如果图表中文显示异常，请安装中文字体

### 解决方案

```bash
# 重新安装依赖
pip install -r requirements.txt --upgrade

# 如果遇到PyTorch安装问题，请访问官网获取适合的安装命令
# https://pytorch.org/get-started/locally/
```

## 扩展实验

可以尝试以下扩展实验：

1. **添加更多模型**: 如GRU、Attention机制等
2. **多变量预测**: 使用多个特征进行预测
3. **不同数据集**: 尝试其他时间序列数据集
4. **集成学习**: 组合多个模型的预测结果
5. **超参数优化**: 使用网格搜索或贝叶斯优化

## 参考文献

1. Hochreiter, S., & Schmidhuber, J. (1997). Long short-term memory. Neural computation, 9(8), 1735-1780.
2. Vaswani, A., et al. (2017). Attention is all you need. Advances in neural information processing systems, 30.
3. Goodfellow, I., Bengio, Y., & Courville, A. (2016). Deep learning. MIT press.

## 联系方式

如有问题，请通过以下方式联系：
- 邮箱: [请填写邮箱]
- 学号: [请填写学号]

---

**实验完成时间**: 预计1-2小时
**难度等级**: 中等
**适用课程**: 深度学习、机器学习、人工智能