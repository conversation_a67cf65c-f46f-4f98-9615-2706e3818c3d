"""
模型训练和评估模块
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import os

class ModelTrainer:
    """模型训练器"""

    def __init__(self, model, device='cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.test_losses = []
        self.train_maes = []
        self.test_maes = []

    def train_epoch(self, train_loader, criterion, optimizer):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_mae = 0
        num_batches = 0

        for sequences, targets in train_loader:
            sequences = sequences.to(self.device)
            targets = targets.to(self.device)

            # 重塑输入数据为 (batch_size, sequence_length, input_size)
            sequences = sequences.unsqueeze(-1)

            optimizer.zero_grad()
            outputs = self.model(sequences)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            mae = torch.mean(torch.abs(outputs - targets))
            total_mae += mae.item()
            num_batches += 1

        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches
        return avg_loss, avg_mae

    def evaluate(self, test_loader, criterion):
        """评估模型"""
        self.model.eval()
        total_loss = 0
        total_mae = 0
        num_batches = 0
        predictions = []
        actuals = []

        with torch.no_grad():
            for sequences, targets in test_loader:
                sequences = sequences.to(self.device)
                targets = targets.to(self.device)

                # 重塑输入数据
                sequences = sequences.unsqueeze(-1)

                outputs = self.model(sequences)
                loss = criterion(outputs, targets)

                total_loss += loss.item()
                mae = torch.mean(torch.abs(outputs - targets))
                total_mae += mae.item()
                num_batches += 1

                predictions.extend(outputs.cpu().numpy())
                actuals.extend(targets.cpu().numpy())

        avg_loss = total_loss / num_batches
        avg_mae = total_mae / num_batches
        return avg_loss, avg_mae, predictions, actuals

    def train(self, train_loader, test_loader, epochs=100, lr=0.001,
              weight_decay=1e-5, patience=10, save_path=None):
        """训练模型"""
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)

        best_test_loss = float('inf')
        patience_counter = 0

        print(f"开始训练，设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters())}")

        for epoch in range(epochs):
            start_time = time.time()

            # 训练
            train_loss, train_mae = self.train_epoch(train_loader, criterion, optimizer)

            # 评估
            test_loss, test_mae, _, _ = self.evaluate(test_loader, criterion)

            # 记录指标
            self.train_losses.append(train_loss)
            self.test_losses.append(test_loss)
            self.train_maes.append(train_mae)
            self.test_maes.append(test_mae)

            # 学习率调度
            scheduler.step(test_loss)

            epoch_time = time.time() - start_time

            print(f"Epoch {epoch+1}/{epochs} ({epoch_time:.2f}s) - "
                  f"Train Loss: {train_loss:.6f}, Train MAE: {train_mae:.6f}, "
                  f"Test Loss: {test_loss:.6f}, Test MAE: {test_mae:.6f}")

            # 早停
            if test_loss < best_test_loss:
                best_test_loss = test_loss
                patience_counter = 0
                if save_path:
                    torch.save(self.model.state_dict(), save_path)
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"早停在第 {epoch+1} 轮")
                    break

        print(f"训练完成，最佳测试损失: {best_test_loss:.6f}")
        return self.train_losses, self.test_losses, self.train_maes, self.test_maes

    def plot_training_history(self, save_path=None):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', linewidth=2)
        ax1.plot(self.test_losses, label='测试损失', linewidth=2)
        ax1.set_title('模型损失曲线', fontsize=14)
        ax1.set_xlabel('Epoch', fontsize=12)
        ax1.set_ylabel('MSE Loss', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # MAE曲线
        ax2.plot(self.train_maes, label='训练MAE', linewidth=2)
        ax2.plot(self.test_maes, label='测试MAE', linewidth=2)
        ax2.set_title('模型MAE曲线', fontsize=14)
        ax2.set_xlabel('Epoch', fontsize=12)
        ax2.set_ylabel('MAE', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def predict_and_plot(self, test_loader, preprocessor, save_path=None):
        """预测并绘制结果"""
        _, _, predictions, actuals = self.evaluate(test_loader, nn.MSELoss())

        # 反标准化
        predictions = preprocessor.inverse_transform(np.array(predictions))
        actuals = preprocessor.inverse_transform(np.array(actuals))

        plt.figure(figsize=(12, 6))
        plt.plot(actuals, label='实际值', marker='o', linewidth=2, markersize=4)
        plt.plot(predictions, label='预测值', marker='s', linewidth=2, markersize=4)
        plt.title('时间序列预测结果', fontsize=16)
        plt.xlabel('时间步', fontsize=12)
        plt.ylabel('乘客数量', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 计算评估指标
        mse = np.mean((predictions - actuals) ** 2)
        mae = np.mean(np.abs(predictions - actuals))
        mape = np.mean(np.abs((predictions - actuals) / actuals)) * 100

        plt.text(0.02, 0.98, f'MSE: {mse:.2f}\nMAE: {mae:.2f}\nMAPE: {mape:.2f}%',
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        return predictions, actuals, {'MSE': mse, 'MAE': mae, 'MAPE': mape}