"""
主实验文件：时间序列神经网络建模实验
实验三：基于时间序列任务的神经网络建模与测试
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_preprocessing import DataPreprocessor
from models import RNNModel, LSTMModel, TransformerModel
from trainer import ModelTrainer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def set_seed(seed=42):
    """设置随机种子"""
    torch.manual_seed(seed)
    np.random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

def run_model_experiment(model_class, model_name, model_params,
                        train_loader, test_loader, preprocessor,
                        device, epochs=50):
    """运行单个模型实验"""
    print(f"\n{'='*50}")
    print(f"开始训练 {model_name} 模型")
    print(f"{'='*50}")

    # 创建模型
    model = model_class(**model_params)
    trainer = ModelTrainer(model, device)

    # 训练模型
    train_losses, test_losses, train_maes, test_maes = trainer.train(
        train_loader, test_loader, epochs=epochs, lr=0.001, patience=15
    )

    # 绘制训练历史
    trainer.plot_training_history()

    # 预测并绘制结果
    predictions, actuals, metrics = trainer.predict_and_plot(test_loader, preprocessor)

    print(f"{model_name} 模型结果:")
    print(f"  MSE: {metrics['MSE']:.4f}")
    print(f"  MAE: {metrics['MAE']:.4f}")
    print(f"  MAPE: {metrics['MAPE']:.4f}%")

    return {
        'model_name': model_name,
        'train_losses': train_losses,
        'test_losses': test_losses,
        'train_maes': train_maes,
        'test_maes': test_maes,
        'predictions': predictions,
        'actuals': actuals,
        'metrics': metrics,
        'model': model
    }

def compare_models(results):
    """比较不同模型的性能"""
    print(f"\n{'='*60}")
    print("模型性能对比")
    print(f"{'='*60}")

    # 创建对比表格
    comparison_data = []
    for result in results:
        comparison_data.append({
            '模型': result['model_name'],
            'MSE': result['metrics']['MSE'],
            'MAE': result['metrics']['MAE'],
            'MAPE (%)': result['metrics']['MAPE'],
            '最终训练损失': result['train_losses'][-1],
            '最终测试损失': result['test_losses'][-1]
        })

    df = pd.DataFrame(comparison_data)
    print(df.to_string(index=False))

    # 绘制对比图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # 训练损失对比
    for result in results:
        ax1.plot(result['train_losses'], label=result['model_name'], linewidth=2)
    ax1.set_title('训练损失对比', fontsize=14)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('MSE Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 测试损失对比
    for result in results:
        ax2.plot(result['test_losses'], label=result['model_name'], linewidth=2)
    ax2.set_title('测试损失对比', fontsize=14)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('MSE Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # MAE对比
    for result in results:
        ax3.plot(result['train_maes'], label=f"{result['model_name']} (训练)", linewidth=2)
        ax3.plot(result['test_maes'], label=f"{result['model_name']} (测试)", linewidth=2, linestyle='--')
    ax3.set_title('MAE对比', fontsize=14)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('MAE')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 预测结果对比
    for result in results:
        ax4.plot(result['predictions'][:20], label=f"{result['model_name']} 预测",
                marker='o', linewidth=2, markersize=4)
    ax4.plot(results[0]['actuals'][:20], label='实际值',
            marker='s', linewidth=3, markersize=4, color='black')
    ax4.set_title('预测结果对比 (前20个点)', fontsize=14)
    ax4.set_xlabel('时间步')
    ax4.set_ylabel('乘客数量')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

def hyperparameter_experiment(model_class, model_name, train_loader, test_loader,
                            preprocessor, device):
    """超参数实验"""
    print(f"\n{'='*50}")
    print(f"{model_name} 超参数实验")
    print(f"{'='*50}")

    # 不同隐藏单元数实验
    hidden_sizes = [32, 64, 128]
    hidden_results = []

    for hidden_size in hidden_sizes:
        print(f"\n测试隐藏单元数: {hidden_size}")

        if model_name == 'Transformer':
            model_params = {'d_model': hidden_size, 'nhead': 4 if hidden_size >= 32 else 2}
        else:
            model_params = {'hidden_size': hidden_size}

        model = model_class(**model_params)
        trainer = ModelTrainer(model, device)

        train_losses, test_losses, train_maes, test_maes = trainer.train(
            train_loader, test_loader, epochs=30, lr=0.001, patience=10
        )

        final_test_loss = test_losses[-1]
        final_test_mae = test_maes[-1]

        hidden_results.append({
            'hidden_size': hidden_size,
            'test_loss': final_test_loss,
            'test_mae': final_test_mae
        })

        print(f"  最终测试损失: {final_test_loss:.6f}")
        print(f"  最终测试MAE: {final_test_mae:.6f}")

    # 绘制超参数实验结果
    hidden_sizes_list = [r['hidden_size'] for r in hidden_results]
    test_losses_list = [r['test_loss'] for r in hidden_results]
    test_maes_list = [r['test_mae'] for r in hidden_results]

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    ax1.plot(hidden_sizes_list, test_losses_list, marker='o', linewidth=2, markersize=8)
    ax1.set_title(f'{model_name} - 隐藏单元数 vs 测试损失', fontsize=14)
    ax1.set_xlabel('隐藏单元数')
    ax1.set_ylabel('测试损失')
    ax1.grid(True, alpha=0.3)

    ax2.plot(hidden_sizes_list, test_maes_list, marker='s', linewidth=2, markersize=8, color='orange')
    ax2.set_title(f'{model_name} - 隐藏单元数 vs 测试MAE', fontsize=14)
    ax2.set_xlabel('隐藏单元数')
    ax2.set_ylabel('测试MAE')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    return hidden_results

def main():
    """主实验函数"""
    print("="*60)
    print("实验三：基于时间序列任务的神经网络建模与测试")
    print("="*60)

    # 设置随机种子
    set_seed(42)

    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 1. 数据准备
    print("\n1. 数据准备...")
    preprocessor = DataPreprocessor(sequence_length=12)

    # 加载数据
    data = preprocessor.load_airline_data()
    print(f"数据概览:")
    print(data.head())
    print(f"数据统计:")
    print(data['Passengers'].describe())

    # 预处理数据
    scaled_data = preprocessor.preprocess_data()

    # 创建数据加载器
    train_loader, test_loader = preprocessor.get_dataloaders(batch_size=16, train_ratio=0.8)

    # 绘制原始数据
    preprocessor.plot_data()

    # 2. 模型实验
    print("\n2. 开始模型实验...")

    # 定义模型配置
    models_config = [
        {
            'class': RNNModel,
            'name': 'RNN',
            'params': {'hidden_size': 64, 'num_layers': 2, 'dropout': 0.2}
        },
        {
            'class': LSTMModel,
            'name': 'LSTM',
            'params': {'hidden_size': 64, 'num_layers': 2, 'dropout': 0.2}
        },
        {
            'class': TransformerModel,
            'name': 'Transformer',
            'params': {'d_model': 64, 'nhead': 8, 'num_layers': 2, 'dropout': 0.1}
        }
    ]

    # 运行所有模型实验
    results = []
    for config in models_config:
        result = run_model_experiment(
            config['class'], config['name'], config['params'],
            train_loader, test_loader, preprocessor, device, epochs=50
        )
        results.append(result)

    # 3. 模型对比
    print("\n3. 模型性能对比...")
    compare_models(results)

    # 4. 超参数实验
    print("\n4. 超参数实验...")

    # 对LSTM进行超参数实验
    lstm_hyperparams = hyperparameter_experiment(
        LSTMModel, 'LSTM', train_loader, test_loader, preprocessor, device
    )

    # 5. 实验总结
    print("\n" + "="*60)
    print("实验总结")
    print("="*60)

    print("\n模型性能排名 (按MSE排序):")
    sorted_results = sorted(results, key=lambda x: x['metrics']['MSE'])
    for i, result in enumerate(sorted_results, 1):
        print(f"{i}. {result['model_name']}: MSE={result['metrics']['MSE']:.4f}, "
              f"MAE={result['metrics']['MAE']:.4f}, MAPE={result['metrics']['MAPE']:.2f}%")

    print(f"\n最佳模型: {sorted_results[0]['model_name']}")
    print(f"最佳MSE: {sorted_results[0]['metrics']['MSE']:.4f}")

    print("\nLSTM超参数实验结果:")
    best_hidden = min(lstm_hyperparams, key=lambda x: x['test_loss'])
    print(f"最佳隐藏单元数: {best_hidden['hidden_size']}")
    print(f"对应测试损失: {best_hidden['test_loss']:.6f}")

    print("\n实验完成！")

if __name__ == "__main__":
    main()