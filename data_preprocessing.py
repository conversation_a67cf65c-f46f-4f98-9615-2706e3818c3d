"""
数据预处理模块
用于加载、预处理航空乘客时间序列数据
"""

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
import os

class TimeSeriesDataset(Dataset):
    """时间序列数据集类"""

    def __init__(self, sequences, targets):
        self.sequences = sequences
        self.targets = targets

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.FloatTensor([self.targets[idx]])

class DataPreprocessor:
    """数据预处理器"""

    def __init__(self, sequence_length=12):
        self.sequence_length = sequence_length
        self.scaler = MinMaxScaler()
        self.data = None
        self.scaled_data = None

    def load_airline_data(self, file_path=None, data_length=2000):
        """
        加载航空乘客数据
        如果没有提供文件路径，则创建长时间序列示例数据

        Args:
            file_path: CSV文件路径（可选）
            data_length: 生成数据的长度（默认2000个数据点，约167年的月度数据）
        """
        if file_path and os.path.exists(file_path):
            # 从文件加载数据
            self.data = pd.read_csv(file_path)
        else:
            # 创建长时间序列数据（模拟真实的复杂时间序列）
            print(f"创建长时间序列数据（{data_length}个数据点）...")

            # 生成时间索引
            dates = pd.date_range(start='1850-01', periods=data_length, freq='M')

            # 创建复杂的时间序列模式
            t = np.arange(data_length)

            # 1. 长期趋势（指数增长）
            long_trend = 100 * np.exp(0.002 * t)

            # 2. 年度季节性（12个月周期）
            annual_seasonal = 80 * np.sin(2 * np.pi * t / 12) + 40 * np.cos(2 * np.pi * t / 12)

            # 3. 多年周期（经济周期，约7-10年）
            economic_cycle = 150 * np.sin(2 * np.pi * t / 84) * (1 + 0.3 * np.sin(2 * np.pi * t / 120))

            # 4. 随机游走成分
            random_walk = np.cumsum(np.random.normal(0, 10, data_length))

            # 5. 白噪声
            noise = np.random.normal(0, 25, data_length)

            # 6. 偶发事件（战争、经济危机等）
            events = np.zeros(data_length)
            # 添加一些随机的负面事件
            event_times = np.random.choice(data_length, size=int(data_length * 0.02), replace=False)
            for event_time in event_times:
                # 事件影响持续6-24个月
                duration = np.random.randint(6, 25)
                end_time = min(event_time + duration, data_length)
                # 负面影响逐渐恢复
                impact = np.linspace(-200, 0, end_time - event_time)
                events[event_time:end_time] += impact

            # 7. 技术革新带来的增长突破
            innovations = np.zeros(data_length)
            innovation_times = [int(data_length * 0.3), int(data_length * 0.6), int(data_length * 0.85)]
            for innovation_time in innovation_times:
                # 技术革新带来的增长
                growth_factor = np.zeros(data_length)
                growth_factor[innovation_time:] = 50 * (1 - np.exp(-0.01 * (t[innovation_time:] - innovation_time)))
                innovations += growth_factor

            # 组合所有成分
            passengers = (long_trend + annual_seasonal + economic_cycle +
                         random_walk + noise + events + innovations)

            # 确保数据为正值
            passengers = np.maximum(passengers, 50)

            # 添加一些异常值（数据质量问题）
            outlier_indices = np.random.choice(data_length, size=int(data_length * 0.005), replace=False)
            for idx in outlier_indices:
                passengers[idx] *= np.random.uniform(1.5, 3.0)  # 异常高值

            self.data = pd.DataFrame({
                'Month': dates,
                'Passengers': passengers.astype(int)
            })

        print(f"数据加载完成，共 {len(self.data)} 个数据点")
        print(f"数据时间跨度: {self.data['Month'].min()} 到 {self.data['Month'].max()}")
        print(f"数据统计: 最小值={self.data['Passengers'].min()}, "
              f"最大值={self.data['Passengers'].max()}, "
              f"平均值={self.data['Passengers'].mean():.0f}")
        return self.data

    def preprocess_data(self):
        """预处理数据"""
        # 提取乘客数量
        passengers = self.data['Passengers'].values.reshape(-1, 1)

        # 数据标准化
        self.scaled_data = self.scaler.fit_transform(passengers)

        print(f"数据预处理完成，数据范围: [{self.scaled_data.min():.3f}, {self.scaled_data.max():.3f}]")
        return self.scaled_data

    def create_sequences(self, data, train_ratio=0.8):
        """创建时间序列序列"""
        sequences = []
        targets = []

        for i in range(len(data) - self.sequence_length):
            seq = data[i:i + self.sequence_length]
            target = data[i + self.sequence_length]
            sequences.append(seq.flatten())
            targets.append(target[0])

        # 划分训练集和测试集
        split_idx = int(len(sequences) * train_ratio)

        train_sequences = sequences[:split_idx]
        train_targets = targets[:split_idx]
        test_sequences = sequences[split_idx:]
        test_targets = targets[split_idx:]

        print(f"序列创建完成:")
        print(f"  训练集: {len(train_sequences)} 个序列")
        print(f"  测试集: {len(test_sequences)} 个序列")
        print(f"  序列长度: {self.sequence_length}")

        return (train_sequences, train_targets), (test_sequences, test_targets)

    def get_dataloaders(self, batch_size=32, train_ratio=0.8):
        """获取数据加载器"""
        if self.scaled_data is None:
            raise ValueError("请先调用 preprocess_data() 方法")

        (train_seq, train_tar), (test_seq, test_tar) = self.create_sequences(
            self.scaled_data, train_ratio
        )

        train_dataset = TimeSeriesDataset(train_seq, train_tar)
        test_dataset = TimeSeriesDataset(test_seq, test_tar)

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

        return train_loader, test_loader

    def inverse_transform(self, scaled_data):
        """反标准化数据"""
        return self.scaler.inverse_transform(scaled_data.reshape(-1, 1))

    def plot_data(self):
        """绘制原始数据"""
        plt.figure(figsize=(12, 6))
        plt.plot(self.data['Month'], self.data['Passengers'], marker='o', linewidth=2)
        plt.title('航空乘客数量时间序列', fontsize=16)
        plt.xlabel('时间', fontsize=12)
        plt.ylabel('乘客数量', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

if __name__ == "__main__":
    # 测试数据预处理
    preprocessor = DataPreprocessor(sequence_length=12)

    # 加载数据
    data = preprocessor.load_airline_data()
    print("\n原始数据前5行:")
    print(data.head())

    # 预处理数据
    scaled_data = preprocessor.preprocess_data()

    # 创建数据加载器
    train_loader, test_loader = preprocessor.get_dataloaders(batch_size=16)

    # 测试数据加载器
    for batch_idx, (sequences, targets) in enumerate(train_loader):
        print(f"\n批次 {batch_idx + 1}:")
        print(f"  序列形状: {sequences.shape}")
        print(f"  目标形状: {targets.shape}")
        if batch_idx == 0:  # 只显示第一个批次
            break

    # 绘制数据
    preprocessor.plot_data()