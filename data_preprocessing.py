"""
数据预处理模块
用于加载、预处理航空乘客时间序列数据
"""

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
import os

class TimeSeriesDataset(Dataset):
    """时间序列数据集类"""

    def __init__(self, sequences, targets):
        self.sequences = sequences
        self.targets = targets

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.FloatTensor([self.targets[idx]])

class DataPreprocessor:
    """数据预处理器"""

    def __init__(self, sequence_length=12):
        self.sequence_length = sequence_length
        self.scaler = MinMaxScaler()
        self.data = None
        self.scaled_data = None

    def load_airline_data(self, file_path=None):
        """
        加载航空乘客数据
        如果没有提供文件路径，则创建示例数据
        """
        if file_path and os.path.exists(file_path):
            # 从文件加载数据
            self.data = pd.read_csv(file_path)
        else:
            # 创建示例航空乘客数据（基于真实数据的模拟）
            print("创建示例航空乘客数据...")
            dates = pd.date_range(start='1949-01', end='1960-12', freq='M')

            # 模拟航空乘客数据的趋势和季节性
            trend = np.linspace(100, 600, len(dates))
            seasonal = 50 * np.sin(2 * np.pi * np.arange(len(dates)) / 12)
            noise = np.random.normal(0, 20, len(dates))
            passengers = trend + seasonal + noise
            passengers = np.maximum(passengers, 50)  # 确保非负

            self.data = pd.DataFrame({
                'Month': dates,
                'Passengers': passengers.astype(int)
            })

        print(f"数据加载完成，共 {len(self.data)} 个数据点")
        return self.data

    def preprocess_data(self):
        """预处理数据"""
        # 提取乘客数量
        passengers = self.data['Passengers'].values.reshape(-1, 1)

        # 数据标准化
        self.scaled_data = self.scaler.fit_transform(passengers)

        print(f"数据预处理完成，数据范围: [{self.scaled_data.min():.3f}, {self.scaled_data.max():.3f}]")
        return self.scaled_data

    def create_sequences(self, data, train_ratio=0.8):
        """创建时间序列序列"""
        sequences = []
        targets = []

        for i in range(len(data) - self.sequence_length):
            seq = data[i:i + self.sequence_length]
            target = data[i + self.sequence_length]
            sequences.append(seq.flatten())
            targets.append(target[0])

        # 划分训练集和测试集
        split_idx = int(len(sequences) * train_ratio)

        train_sequences = sequences[:split_idx]
        train_targets = targets[:split_idx]
        test_sequences = sequences[split_idx:]
        test_targets = targets[split_idx:]

        print(f"序列创建完成:")
        print(f"  训练集: {len(train_sequences)} 个序列")
        print(f"  测试集: {len(test_sequences)} 个序列")
        print(f"  序列长度: {self.sequence_length}")

        return (train_sequences, train_targets), (test_sequences, test_targets)

    def get_dataloaders(self, batch_size=32, train_ratio=0.8):
        """获取数据加载器"""
        if self.scaled_data is None:
            raise ValueError("请先调用 preprocess_data() 方法")

        (train_seq, train_tar), (test_seq, test_tar) = self.create_sequences(
            self.scaled_data, train_ratio
        )

        train_dataset = TimeSeriesDataset(train_seq, train_tar)
        test_dataset = TimeSeriesDataset(test_seq, test_tar)

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

        return train_loader, test_loader

    def inverse_transform(self, scaled_data):
        """反标准化数据"""
        return self.scaler.inverse_transform(scaled_data.reshape(-1, 1))

    def plot_data(self):
        """绘制原始数据"""
        plt.figure(figsize=(12, 6))
        plt.plot(self.data['Month'], self.data['Passengers'], marker='o', linewidth=2)
        plt.title('航空乘客数量时间序列', fontsize=16)
        plt.xlabel('时间', fontsize=12)
        plt.ylabel('乘客数量', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

if __name__ == "__main__":
    # 测试数据预处理
    preprocessor = DataPreprocessor(sequence_length=12)

    # 加载数据
    data = preprocessor.load_airline_data()
    print("\n原始数据前5行:")
    print(data.head())

    # 预处理数据
    scaled_data = preprocessor.preprocess_data()

    # 创建数据加载器
    train_loader, test_loader = preprocessor.get_dataloaders(batch_size=16)

    # 测试数据加载器
    for batch_idx, (sequences, targets) in enumerate(train_loader):
        print(f"\n批次 {batch_idx + 1}:")
        print(f"  序列形状: {sequences.shape}")
        print(f"  目标形状: {targets.shape}")
        if batch_idx == 0:  # 只显示第一个批次
            break

    # 绘制数据
    preprocessor.plot_data()