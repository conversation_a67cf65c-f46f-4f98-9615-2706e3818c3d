"""
测试环境设置和基本功能
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def test_environment():
    """测试环境配置"""
    print("="*50)
    print("环境测试")
    print("="*50)

    # 测试PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")

    # 测试其他库
    print(f"NumPy版本: {np.__version__}")
    print(f"Pandas版本: {pd.__version__}")

    # 测试基本功能
    print("\n基本功能测试:")

    # 创建测试张量
    x = torch.randn(3, 4)
    print(f"测试张量形状: {x.shape}")

    # 测试数据处理
    data = np.random.randn(100)
    df = pd.DataFrame({'value': data})
    print(f"测试数据框形状: {df.shape}")

    print("\n环境测试完成！")

def test_imports():
    """测试模块导入"""
    print("\n模块导入测试:")

    try:
        from data_preprocessing import DataPreprocessor
        print("✓ data_preprocessing 导入成功")
    except ImportError as e:
        print(f"✗ data_preprocessing 导入失败: {e}")

    try:
        from models import RNNModel, LSTMModel, TransformerModel
        print("✓ models 导入成功")
    except ImportError as e:
        print(f"✗ models 导入失败: {e}")

    try:
        from trainer import ModelTrainer
        print("✓ trainer 导入成功")
    except ImportError as e:
        print(f"✗ trainer 导入失败: {e}")

def test_data_preprocessing():
    """测试数据预处理"""
    print("\n数据预处理测试:")

    try:
        from data_preprocessing import DataPreprocessor

        # 创建预处理器
        preprocessor = DataPreprocessor(sequence_length=6)

        # 加载数据
        data = preprocessor.load_airline_data()
        print(f"✓ 数据加载成功，数据点数: {len(data)}")

        # 预处理数据
        scaled_data = preprocessor.preprocess_data()
        print(f"✓ 数据预处理成功，数据范围: [{scaled_data.min():.3f}, {scaled_data.max():.3f}]")

        # 创建数据加载器
        train_loader, test_loader = preprocessor.get_dataloaders(batch_size=8, train_ratio=0.8)
        print(f"✓ 数据加载器创建成功")

        # 测试一个批次
        for sequences, targets in train_loader:
            print(f"✓ 批次测试成功 - 序列形状: {sequences.shape}, 目标形状: {targets.shape}")
            break

    except Exception as e:
        print(f"✗ 数据预处理测试失败: {e}")

def test_models():
    """测试模型创建"""
    print("\n模型创建测试:")

    try:
        from models import RNNModel, LSTMModel, TransformerModel

        # 测试RNN模型
        rnn_model = RNNModel(input_size=1, hidden_size=32, num_layers=1)
        print(f"✓ RNN模型创建成功，参数数量: {sum(p.numel() for p in rnn_model.parameters())}")

        # 测试LSTM模型
        lstm_model = LSTMModel(input_size=1, hidden_size=32, num_layers=1)
        print(f"✓ LSTM模型创建成功，参数数量: {sum(p.numel() for p in lstm_model.parameters())}")

        # 测试Transformer模型
        transformer_model = TransformerModel(input_size=1, d_model=32, nhead=4, num_layers=1)
        print(f"✓ Transformer模型创建成功，参数数量: {sum(p.numel() for p in transformer_model.parameters())}")

        # 测试前向传播
        test_input = torch.randn(2, 6, 1)  # batch_size=2, sequence_length=6, input_size=1

        rnn_output = rnn_model(test_input)
        print(f"✓ RNN前向传播成功，输出形状: {rnn_output.shape}")

        lstm_output = lstm_model(test_input)
        print(f"✓ LSTM前向传播成功，输出形状: {lstm_output.shape}")

        transformer_output = transformer_model(test_input)
        print(f"✓ Transformer前向传播成功，输出形状: {transformer_output.shape}")

    except Exception as e:
        print(f"✗ 模型测试失败: {e}")

if __name__ == "__main__":
    test_environment()
    test_imports()
    test_data_preprocessing()
    test_models()
    print("\n" + "="*50)
    print("所有测试完成！")
    print("如果所有测试都通过，可以运行 python main_experiment.py 开始实验")
    print("="*50)