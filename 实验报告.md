# 实验三：基于时间序列任务的神经网络建模与测试

## 实验信息
- **实验日期**: 2025年06月19日
- **实验者**: [请填写姓名]
- **学号**: [请填写学号]

## 一、实验目的

1. 理解循环神经网络(RNN)和长短期记忆网络(LSTM)的基本原理与网络结构
2. 了解Transformer模型的基本原理与结构
3. 学会构建并训练基于RNN、LSTM、Transformer的序列建模模型，用于时间序列预测任务
4. 探索不同模型结构、超参数对模型性能的影响
5. 提升模型设计、调试与性能分析能力

## 二、实验环境

- **Python版本**: 3.8+
- **深度学习框架**: PyTorch
- **开发环境**: VS Code / Jupyter Notebook
- **主要依赖库**:
  - torch >= 1.9.0
  - numpy >= 1.21.0
  - pandas >= 1.3.0
  - matplotlib >= 3.4.0
  - scikit-learn >= 1.0.0

## 三、数据集说明

### 3.1 数据集选择
本实验使用**航空乘客数据集**，这是一个经典的时间序列数据集，包含1949-1960年的月度航空乘客数量数据。

### 3.2 数据特点
- **时间跨度**: 1949年1月 - 1960年12月 (144个月)
- **数据特征**: 单变量时间序列
- **趋势性**: 数据呈现明显的上升趋势
- **季节性**: 具有明显的年度季节性模式
- **适用性**: 非常适合时间序列预测任务

### 3.3 数据预处理
1. **数据标准化**: 使用MinMaxScaler将数据缩放到[0,1]区间
2. **序列化**: 使用滑动窗口方法，窗口大小为12个月
3. **数据划分**: 训练集80%，测试集20%
4. **批处理**: 批大小设置为16

## 四、模型设计

### 4.1 RNN模型
```python
class RNNModel(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=1, dropout=0.2):
        super(RNNModel, self).__init__()
        self.rnn = nn.RNN(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(dropout)
```

**特点**:
- 基础的循环神经网络结构
- 使用2层RNN，隐藏单元数64
- 添加Dropout防止过拟合

### 4.2 LSTM模型
```python
class LSTMModel(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=1, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(dropout)
```

**特点**:
- 长短期记忆网络，解决RNN的梯度消失问题
- 具有遗忘门、输入门、输出门机制
- 更好地捕获长期依赖关系

### 4.3 Transformer模型
```python
class TransformerModel(nn.Module):
    def __init__(self, input_size=1, d_model=64, nhead=8, num_layers=2,
                 dim_feedforward=256, output_size=1, dropout=0.1):
        super(TransformerModel, self).__init__()
        self.input_projection = nn.Linear(input_size, d_model)
        self.pos_encoder = PositionalEncoding(d_model)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        self.fc = nn.Linear(d_model, output_size)
```

**特点**:
- 基于自注意力机制的现代序列建模方法
- 并行计算能力强，训练效率高
- 能够捕获长距离依赖关系

## 五、实验设计

### 5.1 训练配置
- **损失函数**: MSE (均方误差)
- **优化器**: Adam (学习率: 0.001)
- **训练轮数**: 50 epochs
- **早停机制**: 连续15轮无改善则停止
- **学习率调度**: ReduceLROnPlateau

### 5.2 评估指标
- **MSE (均方误差)**: 衡量预测值与真实值的平方差
- **MAE (平均绝对误差)**: 衡量预测值与真实值的绝对差
- **MAPE (平均绝对百分比误差)**: 衡量相对预测误差

### 5.3 实验内容
1. **基础模型对比**: 比较RNN、LSTM、Transformer三种模型的性能
2. **超参数实验**: 测试不同隐藏单元数对LSTM性能的影响
3. **收敛性分析**: 分析训练过程中损失函数的收敛情况
4. **预测效果可视化**: 绘制预测结果与真实值的对比图

## 六、实验结果

### 6.1 模型性能对比

| 模型 | MSE | MAE | MAPE (%) | 训练时间 |
|------|-----|-----|----------|----------|
| RNN | [运行后填写] | [运行后填写] | [运行后填写] | [运行后填写] |
| LSTM | [运行后填写] | [运行后填写] | [运行后填写] | [运行后填写] |
| Transformer | [运行后填写] | [运行后填写] | [运行后填写] | [运行后填写] |

### 6.2 超参数实验结果

LSTM模型在不同隐藏单元数下的性能：

| 隐藏单元数 | 测试MSE | 测试MAE |
|------------|---------|---------|
| 32 | [运行后填写] | [运行后填写] |
| 64 | [运行后填写] | [运行后填写] |
| 128 | [运行后填写] | [运行后填写] |

### 6.3 训练过程分析

[此处插入训练损失曲线图]

**观察结果**:
- [分析训练收敛情况]
- [分析是否存在过拟合]
- [比较不同模型的收敛速度]

### 6.4 预测结果可视化

[此处插入预测结果对比图]

**分析**:
- [分析预测准确性]
- [分析模型对趋势和季节性的捕获能力]
- [分析异常值处理能力]

## 七、结果分析与讨论

### 7.1 模型性能分析

**RNN模型**:
- 优势: [填写优势]
- 劣势: [填写劣势]
- 适用场景: [填写适用场景]

**LSTM模型**:
- 优势: [填写优势]
- 劣势: [填写劣势]
- 适用场景: [填写适用场景]

**Transformer模型**:
- 优势: [填写优势]
- 劣势: [填写劣势]
- 适用场景: [填写适用场景]

### 7.2 超参数影响分析

**隐藏单元数的影响**:
- [分析不同隐藏单元数对性能的影响]
- [讨论模型复杂度与性能的权衡]

### 7.3 模型收敛性分析

**收敛速度**:
- [比较不同模型的收敛速度]
- [分析影响收敛的因素]

**泛化能力**:
- [分析训练集和测试集性能差异]
- [讨论过拟合问题]

## 八、实验总结

### 8.1 主要发现

1. **模型性能排序**: [根据实验结果填写]
2. **最佳超参数**: [填写最佳配置]
3. **关键影响因素**: [总结影响模型性能的关键因素]

### 8.2 经验总结

1. **数据预处理的重要性**: [总结数据预处理经验]
2. **模型选择策略**: [总结模型选择经验]
3. **超参数调优技巧**: [总结调参经验]

### 8.3 改进方向

1. **数据增强**: [提出数据增强方案]
2. **模型优化**: [提出模型改进方案]
3. **集成学习**: [讨论集成学习的可能性]

## 九、参考文献

1. Hochreiter, S., & Schmidhuber, J. (1997). Long short-term memory. Neural computation, 9(8), 1735-1780.
2. Vaswani, A., et al. (2017). Attention is all you need. Advances in neural information processing systems, 30.
3. Goodfellow, I., Bengio, Y., & Courville, A. (2016). Deep learning. MIT press.

## 十、附录

### 10.1 完整代码

代码文件结构：
```
实验三/
├── requirements.txt          # 依赖包列表
├── data_preprocessing.py     # 数据预处理模块
├── models.py                # 模型定义
├── trainer.py               # 训练器
├── main_experiment.py       # 主实验文件
└── report_generator.py      # 报告生成器
```

### 10.2 运行说明

1. 安装依赖：`pip install -r requirements.txt`
2. 运行实验：`python main_experiment.py`
3. 生成报告：`python report_generator.py`

---

**实验完成日期**: 2025年06月19日
