"""
数据可视化脚本
展示长时间序列数据的特点和复杂性
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from data_preprocessing import DataPreprocessor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def visualize_long_timeseries():
    """可视化长时间序列数据"""

    # 创建数据预处理器
    preprocessor = DataPreprocessor(sequence_length=24)

    # 生成长时间序列数据
    print("生成长时间序列数据用于可视化...")
    data = preprocessor.load_airline_data(data_length=1000)  # 生成1000个数据点用于可视化

    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 完整时间序列
    axes[0, 0].plot(data['Month'], data['Passengers'], linewidth=1, alpha=0.8)
    axes[0, 0].set_title('完整长时间序列数据 (1000个数据点)', fontsize=14)
    axes[0, 0].set_xlabel('时间')
    axes[0, 0].set_ylabel('数值')
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 局部放大（前200个数据点）
    subset_data = data.head(200)
    axes[0, 1].plot(subset_data['Month'], subset_data['Passengers'],
                   linewidth=2, marker='o', markersize=3)
    axes[0, 1].set_title('局部放大视图 (前200个数据点)', fontsize=14)
    axes[0, 1].set_xlabel('时间')
    axes[0, 1].set_ylabel('数值')
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 数据分布直方图
    axes[1, 0].hist(data['Passengers'], bins=50, alpha=0.7, edgecolor='black')
    axes[1, 0].set_title('数据分布直方图', fontsize=14)
    axes[1, 0].set_xlabel('数值')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 移动平均和趋势
    # 计算不同窗口的移动平均
    data['MA_12'] = data['Passengers'].rolling(window=12).mean()
    data['MA_60'] = data['Passengers'].rolling(window=60).mean()
    data['MA_120'] = data['Passengers'].rolling(window=120).mean()

    axes[1, 1].plot(data['Month'], data['Passengers'], alpha=0.3, label='原始数据', linewidth=0.5)
    axes[1, 1].plot(data['Month'], data['MA_12'], label='12期移动平均', linewidth=2)
    axes[1, 1].plot(data['Month'], data['MA_60'], label='60期移动平均', linewidth=2)
    axes[1, 1].plot(data['Month'], data['MA_120'], label='120期移动平均', linewidth=2)
    axes[1, 1].set_title('移动平均趋势分析', fontsize=14)
    axes[1, 1].set_xlabel('时间')
    axes[1, 1].set_ylabel('数值')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 数据统计信息
    print("\n数据统计信息:")
    print(f"数据点数量: {len(data)}")
    print(f"时间跨度: {data['Month'].min()} 到 {data['Month'].max()}")
    print(f"数值范围: {data['Passengers'].min()} 到 {data['Passengers'].max()}")
    print(f"平均值: {data['Passengers'].mean():.2f}")
    print(f"标准差: {data['Passengers'].std():.2f}")
    print(f"变异系数: {data['Passengers'].std() / data['Passengers'].mean():.3f}")

def analyze_seasonality():
    """分析季节性模式"""

    # 创建数据
    preprocessor = DataPreprocessor(sequence_length=24)
    data = preprocessor.load_airline_data(data_length=600)  # 50年数据

    # 添加时间特征
    data['Year'] = data['Month'].dt.year
    data['Month_num'] = data['Month'].dt.month

    # 创建季节性分析图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 按月份的箱线图
    monthly_data = [data[data['Month_num'] == month]['Passengers'].values
                   for month in range(1, 13)]
    axes[0, 0].boxplot(monthly_data, labels=range(1, 13))
    axes[0, 0].set_title('按月份的数据分布', fontsize=14)
    axes[0, 0].set_xlabel('月份')
    axes[0, 0].set_ylabel('数值')
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 年度趋势
    yearly_avg = data.groupby('Year')['Passengers'].mean()
    axes[0, 1].plot(yearly_avg.index, yearly_avg.values, marker='o', linewidth=2)
    axes[0, 1].set_title('年度平均值趋势', fontsize=14)
    axes[0, 1].set_xlabel('年份')
    axes[0, 1].set_ylabel('年平均值')
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 季节性模式热力图
    # 创建年份-月份的数据透视表
    pivot_data = data.pivot_table(values='Passengers', index='Year', columns='Month_num')

    # 只显示部分年份以便观察
    sample_years = pivot_data.index[::5]  # 每5年取一个样本
    sample_data = pivot_data.loc[sample_years]

    im = axes[1, 0].imshow(sample_data.values, cmap='viridis', aspect='auto')
    axes[1, 0].set_title('季节性模式热力图 (采样年份)', fontsize=14)
    axes[1, 0].set_xlabel('月份')
    axes[1, 0].set_ylabel('年份')
    axes[1, 0].set_xticks(range(12))
    axes[1, 0].set_xticklabels(range(1, 13))
    axes[1, 0].set_yticks(range(len(sample_years)))
    axes[1, 0].set_yticklabels(sample_years)
    plt.colorbar(im, ax=axes[1, 0])

    # 4. 自相关分析（简化版）
    # 计算不同滞后期的相关性
    lags = range(1, 61)  # 1-60期滞后
    autocorr = [data['Passengers'].autocorr(lag=lag) for lag in lags]

    axes[1, 1].plot(lags, autocorr, marker='o', markersize=3)
    axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1, 1].axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='0.5阈值')
    axes[1, 1].set_title('自相关函数', fontsize=14)
    axes[1, 1].set_xlabel('滞后期')
    axes[1, 1].set_ylabel('自相关系数')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("="*60)
    print("长时间序列数据可视化分析")
    print("="*60)

    # 可视化长时间序列
    visualize_long_timeseries()

    print("\n" + "="*60)
    print("季节性和周期性分析")
    print("="*60)

    # 分析季节性
    analyze_seasonality()

    print("\n分析完成！")
    print("这个长时间序列数据集包含了:")
    print("1. 长期趋势 - 适合测试模型的趋势捕获能力")
    print("2. 季节性模式 - 适合测试模型的周期性学习能力")
    print("3. 复杂噪声 - 适合测试模型的鲁棒性")
    print("4. 足够长度 - 为深度学习模型提供充足的训练数据")
    print("\n现在可以运行 python main_experiment.py 开始完整实验！")